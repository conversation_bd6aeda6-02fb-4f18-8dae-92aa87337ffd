local WebHook = 'https://discord.com/api/webhooks/1266709115313328160/jU0lELlscEJYAr4_lHXmFfSwFt37mPrFKNas4EgEJsTcsR-_FnyYKVDj26mT5H8e4ljR'

lib.callback.register('z-phone:server:HasPhone', function(source)
    local hasPhone = xCore.HasItemByName(source, 'phone')
    print('^3[Z-PHONE DEBUG]^7 Player ' .. source .. ' has phone: ' .. tostring(hasPhone))
    
    -- Debug supplémentaire pour vérifier l'inventaire
    local playerInventory = exports["inventory"]:GetItemQuantityBy({ source = source }, { name = 'phone' })
    print('^3[Z-PHONE DEBUG]^7 Player ' .. source .. ' phone quantity: ' .. tostring(playerInventory))
    
    return hasPhone
end)

lib.callback.register('z-phone:server:GetWebhook', function(_)
    if WebHook ~= '' then
        return WebHook
    else
        print('Set your webhook to ensure that your camera will work!!!!!! Set this on line 10 of the server sided script!!!!!')
        return nil
    end
end)

-- Commande de test pour donner un téléphone
RegisterCommand('givephone', function(source, args, rawCommand)
    local Player = xCore.GetPlayerBySource(source)
    if Player then
        local result = exports["inventory"]:AddItem({ source = source }, 'phone', 1)
        if result and result.success then
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 0},
                multiline = false,
                args = {"SYSTEM", "Vous avez reçu un téléphone!"}
            })
            print('^3[Z-PHONE DEBUG]^7 Phone given to player ' .. source)
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                multiline = false,
                args = {"SYSTEM", "Erreur lors de l'ajout du téléphone!"}
            })
        end
    end
end, false)